truncate table dim.dim_fin_fr_main_tax_detail_df;
insert into dim.dim_fin_fr_main_tax_detail_df(etl_time, data_id, fr_com_name, tax_code, com_area, com_adr, com_phone_no,
                                              com_bank_name, com_bank_account, com_cont, com_cont_phone_no, email_adr,
                                              svc_phone_no, regi_adr, complaint_phone_no)
select now()                         as etl_time,
       record_id                     as data_id,
       record_com_name               as fr_com_name,
       record_com_taxnum             as tax_code,
       record_com_region             as com_area,
       record_com_address            as com_adr,
       record_com_tel                as com_phone_no,
       record_com_bank               as com_bank_name,
       record_com_bank_account       as com_bank_account,
       record_com_linkman            as com_cont,
       record_com_mobile             as com_cont_phone_no,
       record_com_email_address      as email_adr,
       record_com_service_hotline    as svc_phone_no,
       record_com_regemail           as regi_adr,
       record_com_complaints_hotline as complaint_phone_no
from ods.ods_finer3crm_invoice_com_record_df


