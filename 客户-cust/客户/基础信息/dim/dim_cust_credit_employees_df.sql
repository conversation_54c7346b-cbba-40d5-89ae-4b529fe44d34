/*--********************************************************************--
-- ** 功能描述:
-- ** 创建日期:2025/6/3 14:08
-- ** 修改日志:
eg：1、修改日期 修改人 修改描述
--********************************************************************--*/
TRUNCATE TABLE dim.dim_cust_credit_employees_df;

INSERT INTO dim.dim_cust_credit_employees_df
    (etl_time, uuid, credit_no, name, job)
SELECT NOW() AS etl_time,md5(concat(creditcode,Name)), creditcode, Name, Job
FROM (
         SELECT creditcode
              , Name
              , Job
              , createtime
              , ROW_NUMBER() OVER (PARTITION BY creditcode,Name ORDER BY createtime DESC) rn
         FROM (
                  SELECT UPPER(t.creditcode)                            AS creditcode -- 统一社会信用代码
                       , NULLIF(TRIM(e.employee_object ->> 'Name'), '') AS Name       -- 名称 (string)
                       , NULLIF(TRIM(e.employee_object ->> 'Job'), '')  AS Job        -- 职位 (string)
                       , createtime
                  FROM ods.ods_cloudswiftdata_shuidi_company_baseinfo_df t
                     , LATERAL JSON_ARRAY_ELEMENTS(t.employees::json) AS e(employee_object)
                  WHERE t.employees IS NOT NULL
                    AND t.employees <> '[]' -- 保留现有的过滤器，用于排除 'employees' 是字符串 '[]' 的情况
                    AND t.employees <> ''   -- 增加过滤器，确保 'employees' 不是空字符串
                  UNION ALL
                  SELECT credit_no, name, job, create_time
                  FROM ods.ods_jdy_credit_employee_df
              ) t
     ) t
WHERE rn = 1;