/*--********************************************************************--
-- ** 功能描述:
-- ** 创建日期:2025/6/4 6:33 PM
-- ** 修改日志:
eg：1、修改日期 修改人 修改描述
--********************************************************************--*/
-- DROP TABLE IF EXISTS dim.dim_cust_company_proj_fugou_df;
-- CREATE TABLE dim.dim_cust_company_proj_fugou_df (
--      etl_time   TIMESTAMP      ,
--      com_id   VARCHAR  (50)      ,
--      mgd_com_id   VARCHAR  (50)      ,
--      jx_pm_last   VARCHAR  (255)      ,
--      team_name_last   VARCHA<PERSON>  (255)      ,
--      dmt_year   VARCHAR  (10)      ,
--      dmt_remark   TEXT
-- );
-- COMMENT ON COLUMN dim.dim_cust_company_proj_fugou_df.etl_time IS '入库时间（含时分秒）';
-- COMMENT ON COLUMN dim.dim_cust_company_proj_fugou_df.com_id IS '存档公司id';
-- COMMENT ON COLUMN dim.dim_cust_company_proj_fugou_df.mgd_com_id IS '合并后客户ID';
-- COMMENT ON COLUMN dim.dim_cust_company_proj_fugou_df.jx_pm_last IS '存档公司最近项目责任 PM 名';
-- COMMENT ON COLUMN dim.dim_cust_company_proj_fugou_df.team_name_last IS '存档公司最近项目责任小组名';
-- COMMENT ON COLUMN dim.dim_cust_company_proj_fugou_df.dmt_year IS '存档复购分母年份';
-- COMMENT ON COLUMN dim.dim_cust_company_proj_fugou_df.dmt_remark IS '分母备注';
-- COMMENT ON TABLE dim.dim_cust_company_proj_fugou_df IS '项目复购客户分母';

TRUNCATE TABLE dim.dim_cust_company_proj_fugou_df;
INSERT INTO dim.dim_cust_company_proj_fugou_df( etl_time, com_id, mgd_com_id, jx_pm_last, team_name_last, dmt_year
                                              , dmt_remark)


SELECT NOW()               etl_time
     , com_id
     , COALESCE(mgd_com_id, com_id)
     , recent_pm        AS jx_pm_last
     , recent_team_name AS team_name_last
     , year             AS dmt_year
     , notes            AS dmt_remark
FROM (
         SELECT com_id, recent_pm, recent_team_name, year ::varchar, notes
         FROM temp.t_excel_proj_fugou_dmt_his
         UNION
         SELECT com_id, recent_pm, recent_team_name, year, detail
         FROM temp.t_excel_proj_fugou_dmt_2025
     ) a
         LEFT JOIN (
                       SELECT acq_com_id, mgd_com_id
                       FROM dwd.dwd_cust_mgd_list_df
                   ) b ON a.com_id = b.acq_com_id


