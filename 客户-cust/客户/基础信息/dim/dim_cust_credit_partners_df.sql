/*--********************************************************************--
-- ** 功能描述:
-- ** 创建日期:2025/6/3 14:08
-- ** 修改日志:
eg：1、修改日期 修改人 修改描述
--********************************************************************--*/
TRUNCATE TABLE dim.dim_cust_credit_partners_df;

INSERT INTO dim.dim_cust_credit_partners_df
    ( etl_time, uuid, credit_no, stock_name, stock_type, stock_percent, should_capi, should_date, should_type
    , invest_type, real_capi, capi_date)
SELECT DISTINCT NOW()                                                              AS etl_time
              , MD5(concat(creditcode, stock_name))
              , creditcode
              , stock_name
              , stock_type
              , stock_percent
              , public.convert_to_wan_rmb(should_capi)                             AS should_capi
              , TRIM(REVERSE(SPLIT_PART(REVERSE(should_date), ';', 1)))::timestamp AS should_date
              , TRIM(REVERSE(SPLIT_PART(REVERSE(invest_type), ';', 1)))            AS should_type
              , TRIM(REVERSE(SPLIT_PART(REVERSE(invest_name), ';', 1)))            AS invest_type
              , public.convert_to_wan_rmb(real_capi)                               AS real_capi
              , TRIM(REVERSE(SPLIT_PART(REVERSE(capi_date), ';', 1)))::timestamp   AS capi_date
FROM (
         SELECT UPPER(t.creditcode)                                 AS creditcode    -- 统一社会信用代码
              , NULLIF(TRIM(p.partner_object ->> 'StockName'), '')  AS stock_name    -- 投资人 (string)
              , NULLIF(TRIM(p.partner_object ->> 'StockType'), '')  AS stock_type    -- 投资人类型 (string)
              , (p.partner_object ->> 'StockPercent')::numeric      AS stock_percent -- 出资比例 (string)
              , (p.partner_object ->> 'ShouldCapi')                 AS should_capi   -- 认缴出资额 (string)
              , NULLIF(TRIM(p.partner_object ->> 'ShouldDate'), '') AS should_date   -- 认缴出资时间 (string)
              , NULLIF(TRIM(p.partner_object ->> 'InvestType'), '') AS invest_type   -- 认出资方式 (string)
              , NULLIF(TRIM(p.partner_object ->> 'InvestName'), '') AS invest_name   -- 实出资方式 (string)
              , (p.partner_object ->> 'RealCapi')                   AS real_capi     -- 实缴出资额 (string)
              , NULLIF(TRIM(p.partner_object ->> 'CapiDate'), '')   AS capi_date     -- 实缴时间 (string)
              , rn
         FROM (
                  SELECT creditcode, partners, ROW_NUMBER() OVER (PARTITION BY creditcode ORDER BY createtime DESC) rn
                  FROM ods.ods_cloudswiftdata_shuidi_company_baseinfo_df
              ) t
            , LATERAL JSON_ARRAY_ELEMENTS(t.partners::json) AS p(partner_object)
         WHERE t.partners IS NOT NULL
           AND t.partners <> '[]' -- 保留现有的过滤器，用于排除 'partners' 是字符串 '[]' 的情况
           AND t.partners <> ''
     ) t
WHERE rn = 1;