/*--********************************************************************--
-- ** 功能描述:
-- ** 创建日期:2025/6/3 14:08
--********************************************************************--*/
TRUNCATE TABLE dim.dim_cust_credit_branch_df;

INSERT INTO dim.dim_cust_credit_branch_df( etl_time, uuid, credit_no, branch_credit_no, branch_credit_com_name
                                         , oper_name
                                         , reg_no, belong_org)
SELECT etl_time
     , MD5(CONCAT(creditcode, credit_code))
     , creditcode
     , credit_code
     , name
     , oper_name
     , reg_no
     , belong_org
FROM (
         SELECT NOW()                                              AS etl_time
              , UPPER(t.creditcode)                                AS creditcode  -- 统一社会信用代码
              , NULLIF(TRIM(b.branch_object ->> 'OperName'), '')   AS oper_name   -- 法人信息 (string)
              , NULLIF(TRIM(b.branch_object ->> 'RegNo'), '')      AS reg_no      -- 注册号 (string)
              , NULLIF(TRIM(b.branch_object ->> 'Name'), '')       AS name        -- 名称 (string)
              , NULLIF(TRIM(b.branch_object ->> 'BelongOrg'), '')  AS belong_org  -- 登记机关 (string)
              , NULLIF(TRIM(b.branch_object ->> 'CreditCode'), '') AS credit_code -- 社会统一信用代码 (string)
              , ROW_NUMBER() OVER (PARTITION BY t.creditcode,NULLIF(TRIM(b.branch_object ->> 'CreditCode'), '')) rn1
         FROM (
                  SELECT creditcode, branches, ROW_NUMBER() OVER (PARTITION BY creditcode ORDER BY createtime DESC) rn
                  FROM ods.ods_cloudswiftdata_shuidi_company_baseinfo_df
              ) t
            , LATERAL JSON_ARRAY_ELEMENTS(t.branches::json) AS b(branch_object)
         WHERE t.branches IS NOT NULL
           AND t.branches <> '[]' -- 保留现有的过滤器，用于排除 'branches' 是字符串 '[]' 的情况
           AND t.branches <> ''   -- 增加过滤器，确保 'branches' 不是空字符串
           AND rn = 1
     ) t
WHERE rn1 = 1
  AND credit_code IS NOT NULL;

